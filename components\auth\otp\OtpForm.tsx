"use client";

import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

export default function OtpForm() {
  const [otp, setOtp] = useState(new Array(5).fill(""));
  const [timer, setTimer] = useState(20);
  const router = useRouter();
  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleChange = (element: HTMLInputElement, index: number) => {
    if (isNaN(Number(element.value))) return false;

    setOtp([...otp.map((d, idx) => (idx === index ? element.value : d))]);

    //Focus next input - This will be simplified
    if (element.nextSibling && element.value) {
      (element.nextSibling as HTMLInputElement).focus();
    }
  };

  return (
    <div className="space-y-5 py-5">
      <div className="flex justify-center gap-2 lg:gap-5">
        {otp.map((data, index) => {
          return (
            <Input
              className="w-12 h-12 lg:w-14 lg:h-14 text-center text-lg border border-gray-400 rounded-md"
              type="text"
              name="otp"
              maxLength={1}
              key={index}
              value={data}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                handleChange(e.target, index)
              }
              onFocus={(e: React.FocusEvent<HTMLInputElement>) =>
                e.target.select()
              }
            />
          );
        })}
      </div>
      <Button
        type="submit"
        className="w-full bg-primary text-white p-3 rounded-md font-medium cursor-pointer"
        onClick={() => router.push("/create-account")}
      >
        Verify
      </Button>
      <div className="text-center">
        <Button
          disabled={timer > 0}
          className="text-primary disabled:text-gray-400 font-medium"
        >
          Send Code Again{" "}
          {timer > 0 && `00:${timer.toString().padStart(2, "0")}`}
        </Button>
      </div>
    </div>
  );
}
