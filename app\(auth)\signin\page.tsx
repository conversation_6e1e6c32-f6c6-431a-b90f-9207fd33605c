import SignInForm from "@/components/auth/signin/SignInForm";
import SocialMediaSignIn from "@/components/auth/signin/SocialMediaLogin";
import Image from "next/image";

function SignInPage() {
  return (
    <div className="lg:grid lg:grid-cols-2 lg:gap-20 place-items-center my-5">
      <div className="space-y-5 w-full max-w-lg">
        <h2 className="text-primary text-3xl sm:text-5xl font-bold">
          Welcome Back!
        </h2>
        <h3 className="text-gray-800 text-xl sm:text-3xl font-semibold">
          You’ve been missed!
        </h3>
        <SignInForm />
        <SocialMediaSignIn />
      </div>
      <div className="hidden lg:flex lg:items-center lg:justify-center">
        <Image src="/login.png" alt="logo" width={500} height={500} />
      </div>
    </div>
  );
}

export default SignInPage;
