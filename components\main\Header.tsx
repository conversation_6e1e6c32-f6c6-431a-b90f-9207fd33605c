"use client";

import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { useSidebar } from "@/context/sidebar";
import { Bell, Menu, Search } from "lucide-react";
import Image from "next/image";

export default function Header() {
  const { toggleMobileSidebar } = useSidebar();
  return (
    <header className="flex items-center justify-between p-5 pb-3 border-b">
      {/* Left side: */}
      <div className="flex items-center gap-4">
        <Button
          className="lg:hidden flex items-center gap-2"
          onClick={toggleMobileSidebar}
        >
          <div className="shrink-0">
            <Image src="/favicon.svg" alt="logo" width={50} height={50} />
          </div>
          <Menu />
        </Button>
        <div className="hidden lg:block">
          <h2 className="text-xl font-semibold text-gray-800">Hey <PERSON></h2>
          <p className="text-sm text-gray-500">good morning</p>
        </div>
      </div>
      {/* Right side: */}
      <div className="flex items-center gap-2 md:gap-4">
        <div className="relative hidden sm:block">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <Input
            type="text"
            placeholder="Search workouts..."
            className="pl-10 pr-4 py-2 border rounded-lg bg-white w-48 md:w-64"
          />
        </div>
        <Button className="p-2 border rounded-lg bg-white hidden sm:block">
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5 10H15M2.5 5H17.5M7.5 15H12.5"
              stroke="#344054"
              strokeWidth="1.67"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </Button>
        <div className="flex items-center gap-2">
          <div className="relative w-10 h-10">
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <path
                d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="#e6e6e6"
                strokeWidth="3"
              />
              <path
                d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="#3E1492"
                strokeWidth="3"
                strokeDasharray="0, 100" // 0% progress, adjust this value
                strokeLinecap="round"
              />
              <text
                x="18"
                y="21"
                textAnchor="middle"
                fontSize="10"
                fill="#3E1492"
                fontWeight="bold"
              >
                0/3
              </text>
            </svg>
          </div>
        </div>
        <Button className="relative p-2">
          <Bell className="w-6 h-6 text-gray-600" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </Button>
        <Button className="md:hidden p-2">
          <Search className="w-6 h-6 text-gray-600" />
        </Button>
      </div>
    </header>
  );
}
