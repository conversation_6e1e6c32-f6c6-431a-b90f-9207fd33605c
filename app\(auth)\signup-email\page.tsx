import SignupEmailForm from "@/components/signup/signup-email/SignupEmailForm";
import Image from "next/image";

function SignUpEmailPage() {
  return (
    <div className="lg:grid lg:grid-cols-2 lg:gap-20 place-items-center my-5">
      <div className="space-y-5 w-full max-w-lg">
        <div className="space-y-3">
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-800">
            SIGN UP <span className="text-primary">WITH EMAIL</span>
          </h1>
          <p className="text-gray-400 font-medium">
            Begin With Creating New Free Account. This Helps You Keep Your
            Health And Fitness
          </p>
        </div>
        <SignupEmailForm />
      </div>
      <div className="hidden lg:flex lg:items-center lg:justify-center">
        <Image
          src="/signup.png"
          alt="sign up illustration"
          width={500}
          height={500}
        />
      </div>
    </div>
  );
}

export default SignUpEmailPage;
