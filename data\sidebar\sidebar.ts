import {
  Compass,
  FileText,
  HeartPulse,
  Home,
  Info,
  <PERSON>tings,
  ShieldCheck,
  User,
} from "lucide-react";

export const mainNavItems = [
  { href: "/", icon: Home, label: "Home" },
  { href: "/personal", icon: User, label: "Personal" },
  { href: "/my-fit", icon: HeartPulse, label: "My Fit" },
  { href: "/discover", icon: Compass, label: "Discover" },
  { href: "/settings", icon: Settings, label: "Setting" },
];

export const secondaryNavItems = [
  { href: "/about", icon: Info, label: "About US" },
  { href: "/privacy", icon: ShieldCheck, label: "Privacy Policy" },
  { href: "/terms", icon: FileText, label: "Terms & Conditions" },
];
