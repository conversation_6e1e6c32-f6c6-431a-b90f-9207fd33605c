import { usePathname } from 'next/navigation';
import { UserCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { mainNavItems, secondaryNavItems } from '@/data/sidebar/sidebar';
import Link from 'next/link';
import Image from 'next/image';
export default function SidebarContent({
  isExpanded,
  onLinkClick,
}: {
  isExpanded: boolean;
  onLinkClick?: () => void;
}) {
  const pathname = usePathname();
  return (
    <>
      <div className='py-5 px-2 flex items-center gap-2'>
        <div className={cn('shrink-0', !isExpanded && 'mx-auto')}>
          <Image
            src='/favicon.svg'
            alt='logo'
            width={50}
            height={50}
            className='transition-transform duration-300'
          />
        </div>
        {/* <Dumbbell
          className={cn(
            "w-8 h-8 text-primary transition-transform duration-300",
            !isExpanded && "mx-auto"
          )}
        /> */}
        {isExpanded && (
          <h1
            className={cn(
              'text-xl font-bold text-primary whitespace-nowrap overflow-hidden transition-all duration-300'
            )}
          >
            SHERIF FARANCA
          </h1>
        )}
      </div>
      <nav className='flex-1 px-2 space-y-2'>
        {mainNavItems.map((item) => (
          <Link
            key={item.label}
            href={item.href}
            onClick={() => onLinkClick?.()}
            className={cn(
              'flex items-center gap-3 py-3 rounded-lg text-secondary font-medium hover:bg-purple-100 hover:text-primary transition-colors',
              isExpanded ? 'px-4 justify-start' : 'justify-center',
              pathname === item.href &&
                'bg-primary text-white hover:bg-primary/90'
            )}
          >
            <item.icon className='w-6 h-6 shrink-0' />
            {isExpanded && (
              <span
                className={cn(
                  'whitespace-nowrap overflow-hidden transition-all duration-300'
                )}
              >
                {item.label}
              </span>
            )}
          </Link>
        ))}
      </nav>
      <div className='px-2 py-6 space-y-2 border-t'>
        {secondaryNavItems.map((item) => (
          <Link
            key={item.label}
            href={item.href}
            onClick={onLinkClick}
            className={cn(
              'flex items-center gap-3 py-3 rounded-lg text-secondary font-medium hover:bg-gray-100 transition-colors',
              isExpanded ? 'px-4 justify-start' : 'justify-center'
            )}
          >
            <item.icon className='w-6 h-6 shrink-0' />
            {isExpanded && (
              <span
                className={cn(
                  'whitespace-nowrap overflow-hidden transition-all duration-300'
                )}
              >
                {item.label}
              </span>
            )}
          </Link>
        ))}
      </div>
      <div className='p-4 border-t'>
        <div className='flex items-center gap-3'>
          <div className='w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center shrink-0'>
            <UserCircle className='w-8 h-8 text-gray-400' />
          </div>
          {isExpanded && (
            <div
              className={cn(
                'whitespace-nowrap overflow-hidden transition-all duration-300'
              )}
            >
              <p className='font-semibold text-gray-800'>Hey Sara Ali</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
