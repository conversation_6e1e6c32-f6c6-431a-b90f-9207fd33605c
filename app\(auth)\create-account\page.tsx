import CreateAccountForm from "@/components/auth/create-profile/CreateAccountForm";
import Image from "next/image";

function CreateAccountPage() {
  return (
    <div className="lg:grid lg:grid-cols-2 lg:gap-20 place-items-center my-5">
      <div className="space-y-3 flex flex-col justify-center w-full max-w-xl">
        <div className="space-y-3">
          <h1 className="font-bold text-gray-800 text-3xl sm:text-4xl">
            MOMENT TO <span className="text-primary">BEGINNING</span>
          </h1>
        </div>
        <CreateAccountForm />
      </div>
      <div className="hidden lg:flex lg:items-center lg:justify-center">
        <Image
          src="/create-account.png"
          alt="Create account"
          width={500}
          height={500}
        />
      </div>
    </div>
  );
}

export default CreateAccountPage;
